<?php

namespace Modules\Contacts\Imports;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Modules\Contacts\Models\Contact;
use Modules\Contacts\Models\Field;
use Maatwebsite\Excel\Concerns\WithChunkReading;


class ContactsImport implements ToModel, WithHeadingRow, WithChunkReading
{
    public $importedCount = 0;
    public $skippedCount = 0;
    public $errorRows = [];

    public function chunkSize(): int
    {
        return 150;
    }

  

    /**
     * @param array $row
     *
     * @return Contact|null
     */
    public function model(array $row)
    {
        // Validate required fields
        if (!isset($row['phone']) || !isset($row['name']) ||
            empty(trim($row['phone'])) || empty(trim($row['name']))) {
            $this->errorRows[] = 'Missing required phone or name data';
            return null;
        }

        // Clean the data
        $phone = trim($row['phone']);
        $name = trim($row['name']);

        // Smart phone number formatting
        $phone = $this->formatPhoneNumber($phone);

        if (!$phone) {
            $this->errorRows[] = 'Invalid phone number format: ' . trim($row['phone']);
            return null;
        }

        // Check if contact already exists
        $prevContact = Contact::where('phone', $phone)->first();
        if ($prevContact) {
            $this->skippedCount++;
            return $prevContact;
        }

        $keys = array_keys($row);
        $keysForFields = [];
        foreach ($keys as $key => $value) {
            $keysForFields[$key] = $this->getOrMakeField($value);
        }

        // Create new contact with validated data
        $contact = new Contact([
           'name' => $name,
           'phone' => $phone,
        ]);
        $contact->save();

        // Handle email if present
        if (isset($row['email']) && !empty(trim($row['email']))) {
            $contact->email = trim($row['email']);
        }

        // Handle avatar if present
        if (isset($row['avatar']) && !empty(trim($row['avatar']))) {
            $contact->avatar = trim($row['avatar']);
        }

        // Attach custom fields
        foreach ($keysForFields as $key => $fieldID) {
            if ($fieldID != 0 && isset($row[$keys[$key]]) && !empty(trim($row[$keys[$key]]))) {
                $contact->fields()->attach($fieldID, ['value' => trim($row[$keys[$key]])]);
            }
        }
        $contact->update();

        $this->importedCount++;
        return $contact;
    }

    private function formatPhoneNumber($phone)
    {
        // Remove all non-digit characters except +
        $phone = preg_replace('/[^\d+]/', '', $phone);

        // If already has + prefix, validate and return
        if (strpos($phone, '+') === 0) {
            return strlen($phone) >= 11 ? $phone : false; // Minimum: +1234567890
        }

        // Handle different number formats without country code
        if (strlen($phone) == 10) {
            // 10 digits - could be multiple countries, use smart detection
            return $this->detectCountryAndFormat($phone);
        } elseif (strlen($phone) == 11 && strpos($phone, '0') === 0) {
            // 11 digits starting with 0 - likely Indian landline format, remove 0 and add +91
            return '+91' . substr($phone, 1);
        } elseif (strlen($phone) == 12 && strpos($phone, '91') === 0) {
            // 12 digits starting with 91 - Indian number without +, add +
            return '+' . $phone;
        } elseif (strlen($phone) >= 10) {
            // Other formats - just add + prefix (user should specify country code for clarity)
            return '+' . $phone;
        }

        // Invalid format
        return false;
    }

    private function detectCountryAndFormat($phone)
    {
        // For 10-digit numbers, we need to make an educated guess
        // This is inherently ambiguous, so we'll use some heuristics

        // Check if it looks like an Indian mobile number (starts with 6-9)
        if (preg_match('/^[6-9]\d{9}$/', $phone)) {
            return '+91' . $phone; // Indian mobile numbers start with 6, 7, 8, or 9
        }

        // Check if it looks like a US number (common patterns)
        if (preg_match('/^[2-9]\d{2}[2-9]\d{6}$/', $phone)) {
            return '+1' . $phone; // US format: NXX-NXX-XXXX where N=2-9, X=0-9
        }

        // For other 10-digit numbers, default to adding just + (ambiguous case)
        // User should ideally provide country code for clarity
        return '+' . $phone;
    }

    private function getOrMakeField($field_name){
        // These fields are direct Contact model properties, not custom fields
        if($field_name=="name"||$field_name=="phone"||$field_name=="avatar"||$field_name=="email"){
            return 0;
        }
        $field=Field::where('name',$field_name)->first();
        if(!$field){
            $field=Field::create([
                'name'     => $field_name,
                'type'=>"text",
            ]);
            $field->save();
        }
        return $field->id;
    }

}