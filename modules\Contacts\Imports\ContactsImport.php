<?php

namespace Modules\Contacts\Imports;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Modules\Contacts\Models\Contact;
use Modules\Contacts\Models\Field;
use Maatwebsite\Excel\Concerns\WithChunkReading;


class ContactsImport implements ToModel, WithHeadingRow, WithChunkReading
{
    public $importedCount = 0;
    public $skippedCount = 0;
    public $errorRows = [];

    public function chunkSize(): int
    {
        return 150;
    }

  

    /**
     * @param array $row
     *
     * @return Contact|null
     */
    public function model(array $row)
    {
        // Validate required fields
        if (!isset($row['phone']) || !isset($row['name']) ||
            empty(trim($row['phone'])) || empty(trim($row['name']))) {
            $this->errorRows[] = 'Missing required phone or name data';
            return null;
        }

        // Clean the data
        $phone = trim($row['phone']);
        $name = trim($row['name']);

        // Validate phone number length
        if (strlen($phone) < 10) {
            $this->errorRows[] = 'Phone number too short: ' . $phone;
            return null;
        }

        // Add + prefix if not present
        if (strpos($phone, "+") !== 0) {
            $phone = "+" . $phone;
        }

        // Check if contact already exists
        $prevContact = Contact::where('phone', $phone)->first();
        if ($prevContact) {
            $this->skippedCount++;
            return $prevContact;
        }

        $keys = array_keys($row);
        $keysForFields = [];
        foreach ($keys as $key => $value) {
            $keysForFields[$key] = $this->getOrMakeField($value);
        }

        // Create new contact with validated data
        $contact = new Contact([
           'name' => $name,
           'phone' => $phone,
        ]);
        $contact->save();

        // Handle avatar if present
        if (isset($row['avatar']) && !empty(trim($row['avatar']))) {
            $contact->avatar = trim($row['avatar']);
        }

        // Attach custom fields
        foreach ($keysForFields as $key => $fieldID) {
            if ($fieldID != 0 && isset($row[$keys[$key]]) && !empty(trim($row[$keys[$key]]))) {
                $contact->fields()->attach($fieldID, ['value' => trim($row[$keys[$key]])]);
            }
        }
        $contact->update();

        $this->importedCount++;
        return $contact;
    }

    private function getOrMakeField($field_name){
        if($field_name=="name"||$field_name=="phone"||$field_name=="avatar"){
            return 0;
        }
        $field=Field::where('name',$field_name)->first();
        if(!$field){
            $field=Field::create([
                'name'     => $field_name,
                'type'=>"text",
            ]);
            $field->save();
        }
        return $field->id;
    }

}