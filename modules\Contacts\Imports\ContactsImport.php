<?php

namespace Modules\Contacts\Imports;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Modules\Contacts\Models\Contact;
use Modules\Contacts\Models\Field;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class ContactsImport implements ToModel, WithHeadingRow, WithChunkReading
{

    public function chunkSize(): int
    {
        return 150;
    }

  

    /**
     * @param array $row
     *
     * @return Contact|null
     */
    public function model(array $row)
    {
        // Check if required columns exist
        if (!isset($row['phone']) || !isset($row['name'])) {
            // Skip this row if required columns are missing
            return null;
        }

        // Skip empty rows
        if (empty(trim($row['phone'])) && empty(trim($row['name']))) {
            return null;
        }

        $keys = array_keys($row);
        $keysForFields=[];
        foreach ($keys as $key => $value) {
            $keysForFields[$key]=$this->getOrMakeField($value);
        }

        // Check if contact already exists
        $prevContact=Contact::where('phone', $row['phone'])->first();
        if($prevContact){
            return $prevContact;
        }

        // Create new contact with validated data
        $contact=new Contact([
           'name'     => trim($row['name']),
           'phone'    => strpos($row['phone'],"+")!=false?$row['phone']:"+".$row['phone'],
        ]);
        $contact->save();

        // Handle avatar if present
        if(isset($row['avatar']) && !empty(trim($row['avatar']))){
            $contact->avatar=$row['avatar'];
        }

        // Attach custom fields
        foreach ($keysForFields as $key => $fieldID) {
            if($fieldID!=0 && isset($row[$keys[$key]]) && !empty(trim($row[$keys[$key]]))){
                $contact->fields()->attach($fieldID, ['value' =>  trim($row[$keys[$key]])]);
            }
        }
        $contact->update();


        return $contact;
    }

    private function getOrMakeField($field_name){
        if($field_name=="name"||$field_name=="phone"||$field_name=="avatar"){
            return 0;
        }
        $field=Field::where('name',$field_name)->first();
        if(!$field){
            $field=Field::create([
                'name'     => $field_name,
                'type'=>"text",
            ]);
            $field->save();
        }
        return $field->id;
    }

}