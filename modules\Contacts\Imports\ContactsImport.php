<?php

namespace Modules\Contacts\Imports;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Modules\Contacts\Models\Contact;
use Modules\Contacts\Models\Field;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Validators\Failure;

class ContactsImport implements ToModel, WithHeadingRow, WithChunkReading, WithValidation, SkipsOnFailure
{
    public $importedCount = 0;
    public $skippedCount = 0;
    public $failures = [];

    public function chunkSize(): int
    {
        return 150;
    }

    public function rules(): array
    {
        return [
            'phone' => 'required|string|min:10',
            'name' => 'required|string|min:1',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'phone.required' => 'Phone number is required',
            'phone.min' => 'Phone number must be at least 10 characters',
            'name.required' => 'Name is required',
            'name.min' => 'Name cannot be empty',
        ];
    }

    public function onFailure(Failure ...$failures)
    {
        $this->failures = array_merge($this->failures, $failures);
    }

  

    /**
     * @param array $row
     *
     * @return Contact|null
     */
    public function model(array $row)
    {
        // Validation is handled by WithValidation, so we can assume data is valid here

        // Clean the data
        $phone = trim($row['phone']);
        $name = trim($row['name']);

        // Add + prefix if not present
        if (strpos($phone, "+") !== 0) {
            $phone = "+" . $phone;
        }

        // Check if contact already exists
        $prevContact = Contact::where('phone', $phone)->first();
        if ($prevContact) {
            $this->skippedCount++;
            return $prevContact;
        }

        $keys = array_keys($row);
        $keysForFields = [];
        foreach ($keys as $key => $value) {
            $keysForFields[$key] = $this->getOrMakeField($value);
        }

        // Create new contact with validated data
        $contact = new Contact([
           'name' => $name,
           'phone' => $phone,
        ]);
        $contact->save();

        // Handle avatar if present
        if (isset($row['avatar']) && !empty(trim($row['avatar']))) {
            $contact->avatar = trim($row['avatar']);
        }

        // Attach custom fields
        foreach ($keysForFields as $key => $fieldID) {
            if ($fieldID != 0 && isset($row[$keys[$key]]) && !empty(trim($row[$keys[$key]]))) {
                $contact->fields()->attach($fieldID, ['value' => trim($row[$keys[$key]])]);
            }
        }
        $contact->update();

        $this->importedCount++;
        return $contact;
    }

    private function getOrMakeField($field_name){
        if($field_name=="name"||$field_name=="phone"||$field_name=="avatar"){
            return 0;
        }
        $field=Field::where('name',$field_name)->first();
        if(!$field){
            $field=Field::create([
                'name'     => $field_name,
                'type'=>"text",
            ]);
            $field->save();
        }
        return $field->id;
    }

}