@extends('layouts.app', ['title' =>  __("CSV contacts Import ") ])


@section('content')
    <div class="header  pb-8 pt-5 pt-md-8">
    </div>
    <div class="container-fluid mt--7">
        <div class="row">
            <div class="col">
                <div class="card shadow">
                    <div class="card-header border-0">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0">{{ __("CSV contacts Import ") }}</h3>
                  
   
                                
                            </div>
                            
                               
                        </div>
                       
                    </div>

                    <div class="col-12">
                        @include('partials.flash')
                    </div>

                   
                       <div class="card-body">
                            <!-- CSV Format Instructions -->
                            <div class="alert alert-info mb-4">
                                <h5><i class="fas fa-info-circle"></i> CSV Format Requirements</h5>
                                <p class="mb-2"><strong>Required columns:</strong> <code>phone</code> and <code>name</code></p>
                                <p class="mb-2"><strong>Optional columns:</strong> <code>custom_field_name_1</code>, <code>custom_field_name_2</code>, <code>avatar</code></p>
                                <p class="mb-2"><strong>Example CSV format:</strong></p>
                                <pre class="bg-light p-2 rounded">phone,name,custom_field_name_1,custom_field_name_2
+1234567890,John Doe,Company A,Manager
+9876543210,Jane Smith,Company B,Developer</pre>
                                <p class="mb-0"><small class="text-muted">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Phone numbers should include country code (e.g., +1234567890). If no + prefix is provided, it will be added automatically.
                                </small></p>
                            </div>

                            <form action="{{ route('contacts.import.store') }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                @include('partials.input',['additionalInfo'=>"Required headers: phone, name (Optional: custom_field_name_1, custom_field_name_2)",'class'=>'col-md-6','name'=>"CSV file",'id'=>'csv','type'=>'file','placeholder'=>"",'required'=>true,'accept'=>".csv"])
                                @include('partials.select',['class'=>'col-md-6','name'=>"Group to insert into",'id'=>'group','placeholder'=>"",'required'=>false,'data'=>$groups])
                                <div class="form-group">
                                    <button type="submit" class="btn btn-success ml-3 mt-2" >{{ __('Import contacts')}}</button>
                                </div>

                            </form>
                        </div>
                   
                    
     
         


                </div>
            </div>
        </div>

        @include('layouts.footers.auth')
    </div>
@endsection
